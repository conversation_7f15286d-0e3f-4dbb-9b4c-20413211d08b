package com.yuelan.plugins.satoken;


import cn.dev33.satoken.log.SaLog;
import cn.dev33.satoken.stp.StpInterface;
import cn.dev33.satoken.strategy.SaStrategy;
import com.yuelan.plugins.satoken.able.IAuthorization;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.core.annotation.AnnotatedElementUtils;

@AutoConfiguration
public class SaTokenRegister {

    /**
     * 重写 Sa-Token 框架内部算法策略
     */
    @Autowired
    public void rewriteSaStrategy() {
        // 重写Sa-Token的注解处理器，增加注解合并功能
        SaStrategy.instance.getAnnotation = AnnotatedElementUtils::getMergedAnnotation;
    }

    @Bean
    @ConditionalOnMissingBean(SaTokenHelper.class)
    @ConditionalOnBean(IAuthorization.class)
    public SaTokenHelper saTokenHelper() {
        return new SaTokenHelper();
    }

    @Bean
    @ConditionalOnMissingBean(StpInterface.class)
    @ConditionalOnBean(IAuthorization.class)
    public StpInterface stpInterfaceImpl() {
        return new StpInterfaceImpl();
    }

    @Bean
    @ConditionalOnMissingBean(SaLogForSlf4j.class)
    public SaLog saLogForSlf4j() {
        return new SaLogForSlf4j();
    }

}
