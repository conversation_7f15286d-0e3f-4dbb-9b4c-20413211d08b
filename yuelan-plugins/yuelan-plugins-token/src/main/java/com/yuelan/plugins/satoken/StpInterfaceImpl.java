package com.yuelan.plugins.satoken;


import cn.dev33.satoken.stp.StpInterface;
import com.yuelan.plugins.satoken.able.IAuthorization;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 自定义权限验证接口扩展
 */
public class StpInterfaceImpl implements StpInterface {

    @Resource
    private SaTokenHelper saTokenHelper;


    /**
     * 返回一个账号所拥有的权限码集合
     */
    @Override
    public List<String> getPermissionList(Object loginId, String loginType) {
        IAuthorization IAuthorization = saTokenHelper.getAuthorization(loginType);
        if (Objects.isNull(IAuthorization)) {
            return new ArrayList<>();
        }
        return IAuthorization.getPermissionList(loginId);
    }

    /**
     * 返回一个账号所拥有的角色标识集合 (权限与角色可分开校验)
     */
    @Override
    public List<String> getRoleList(Object loginId, String loginType) {
        IAuthorization IAuthorization = saTokenHelper.getAuthorization(loginType);
        if (Objects.isNull(IAuthorization)) {
            return new ArrayList<>();
        }
        return IAuthorization.getRoleList(loginId);
    }

}
