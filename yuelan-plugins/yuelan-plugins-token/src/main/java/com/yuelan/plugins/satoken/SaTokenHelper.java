package com.yuelan.plugins.satoken;

import com.google.common.collect.Maps;
import com.yuelan.plugins.satoken.able.IAuthorization;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;

import java.util.Map;

public class <PERSON><PERSON>okenHelper implements ApplicationContextAware {

    private static final Map<String, IAuthorization> AUTHORIZATION_MAP = Maps.newHashMap();

    public IAuthorization getAuthorization(String type) {
        return AUTHORIZATION_MAP.get(type);
    }


    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        Map<String, IAuthorization> map = applicationContext.getBeansOfType(IAuthorization.class);
        map.forEach((key, value) -> AUTHORIZATION_MAP.put(value.getLoginType(), value));
    }
}
