package com.yuelan.plugins.log.context;

import com.yuelan.plugins.log.enums.OperationType;
import lombok.Data;

@Data
public class LoginLog {
    /**
     * 登录类型
     */
    private String loginType;
    /**
     * 登录用户ID
     */
    private String userId;
    /**
     * 用户名
     */
    private String username;
    /**
     * 操作类型
     */
    private OperationType type;
    /**
     * 请求IP
     */
    private String ipAddr;
    /**
     * User-Agent
     */
    private String userAgent;
    /**
     * 访问凭证
     */
    private String token;
}
