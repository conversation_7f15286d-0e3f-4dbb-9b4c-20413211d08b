package com.yuelan.plugins.log.annotation;


import com.yuelan.plugins.log.enums.OperationType;

import java.lang.annotation.*;

/**
 * 自定义操作日志记录注解
 */
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.METHOD, ElementType.TYPE})
@Documented
public @interface Log {

    /**
     * 多账号体系下所属的账号体系标识，非多账号体系无需关注此值
     */
    String login() default "";

    /**
     * 日志标题
     */
    String title() default "";

    /**
     * 操作类型
     */
    OperationType type() default OperationType.OTHER;

    /**
     * 是否保存请求的参数
     */
    boolean isSaveRequestData() default true;

    /**
     * 是否保存响应的参数
     */
    boolean isSaveResponseData() default true;
}
