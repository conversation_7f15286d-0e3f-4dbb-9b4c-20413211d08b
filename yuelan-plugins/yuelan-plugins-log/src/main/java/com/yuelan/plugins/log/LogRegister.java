package com.yuelan.plugins.log;


import com.yuelan.plugins.log.aspect.LogAspect;
import com.yuelan.plugins.log.listener.DefaultLogListener;
import com.yuelan.plugins.log.listener.LogListenerHelper;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;

@AutoConfiguration
public class LogRegister {
    @Bean
    @ConditionalOnMissingBean(DefaultLogListener.class)
    public DefaultLogListener logListener() {
        return new DefaultLogListener();
    }

    @Bean
    @ConditionalOnMissingBean(LogListenerHelper.class)
    public LogListenerHelper logListenerHelper() {
        return new LogListenerHelper();
    }

    @Bean
    @ConditionalOnMissingBean(LogAspect.class)
    public LogAspect logAspect() {
        return new LogAspect();
    }

}
