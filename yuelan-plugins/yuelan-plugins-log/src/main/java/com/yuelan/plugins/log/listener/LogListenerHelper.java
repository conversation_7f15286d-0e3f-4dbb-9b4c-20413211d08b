package com.yuelan.plugins.log.listener;

import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Maps;
import com.yuelan.plugins.log.LogConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;

import java.util.Map;
import java.util.Objects;

@Slf4j
public class LogListenerHelper implements ApplicationContextAware {

    private static final Map<String, LogListener> LOG_LISTENER_MAP = Maps.newHashMap();

    public LogListener getListener(String loginType) {
        if (StrUtil.isNotEmpty(loginType)) {
            LogListener logListener = LOG_LISTENER_MAP.get(loginType);
            if (Objects.nonNull(logListener)) {
                return logListener;
            }
            return LOG_LISTENER_MAP.get(LogConstants.DEFAULT_LOGIN_TYPE);
        }
        if (LOG_LISTENER_MAP.size() == 2) {
            for (LogListener listener : LOG_LISTENER_MAP.values()) {
                if (!Objects.equals(listener.getLoginType(), LogConstants.DEFAULT_LOGIN_TYPE)) {
                    return listener;
                }
            }
        } else {
            log.warn("有多个账号体系，请指定账号体系标识");
        }
        return LOG_LISTENER_MAP.get(LogConstants.DEFAULT_LOGIN_TYPE);
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        Map<String, LogListener> map = applicationContext.getBeansOfType(LogListener.class);
        map.forEach((key, value) -> LOG_LISTENER_MAP.put(value.getLoginType(), value));
    }
}
