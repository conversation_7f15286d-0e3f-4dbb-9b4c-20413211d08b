package com.yuelan.plugins.log.enums;

import com.yuelan.result.able.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 操作状态
 */
@Getter
@AllArgsConstructor
public enum OperationStatus implements IEnum<OperationStatus> {

    SUCCESS(0, "成功"),
    FAIL(1, "失败"),
    //
    ;
    private Integer code;
    private String desc;

    @Override
    public OperationStatus getDefault() {
        return null;
    }
}
