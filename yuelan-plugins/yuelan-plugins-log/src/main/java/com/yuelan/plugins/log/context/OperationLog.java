package com.yuelan.plugins.log.context;

import com.yuelan.plugins.log.enums.OperationStatus;
import com.yuelan.plugins.log.enums.OperationType;
import lombok.Data;

@Data
public class OperationLog {

    /**
     * 登录类型
     */
    private String loginType;
    /**
     * 操作人员
     */
    private String userId;
    /**
     * 日志标题
     */
    private String title;
    /**
     * 操作类型
     */
    private OperationType type;
    /**
     * 请求方式
     */
    private String requestMethod;
    /**
     * 请求url
     */
    private String requestURI;
    /**
     * 请求参数
     */
    private String requestData;
    /**
     * 返回参数
     */
    private String responseData;
    /**
     * 请求IP
     */
    private String ipAddr;
    /**
     * User-Agent
     */
    private String userAgent;
    /**
     * 操作状态（0正常 1异常）
     */
    private OperationStatus status;
    /**
     * 提示消息
     */
    private String message;
}
