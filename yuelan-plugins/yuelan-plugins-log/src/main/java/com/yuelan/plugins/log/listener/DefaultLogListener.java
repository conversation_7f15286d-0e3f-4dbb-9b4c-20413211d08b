package com.yuelan.plugins.log.listener;

import com.alibaba.fastjson2.JSON;
import com.yuelan.plugins.log.LogConstants;
import com.yuelan.plugins.log.context.LogContext;
import com.yuelan.plugins.log.context.OperationLog;
import com.yuelan.plugins.log.utils.LogUtil;
import lombok.extern.slf4j.Slf4j;

import javax.servlet.http.HttpServletRequest;


@Slf4j
public class DefaultLogListener implements LogListener {

    @Override
    public String getLoginType() {
        return LogConstants.DEFAULT_LOGIN_TYPE;
    }

    @Override
    public void listener(LogContext logContext, HttpServletRequest httpServletRequest) {
        OperationLog operationLog = new OperationLog();
        operationLog.setLoginType(logContext.getLogin());
//        operationLog.setUserId();
        operationLog.setTitle(logContext.getTitle());
        operationLog.setType(logContext.getType());
        operationLog.setRequestURI(logContext.getRequestURI());
        operationLog.setRequestMethod(logContext.getRequestMethod());
        operationLog.setRequestData(logContext.getRequestData());
        operationLog.setResponseData(logContext.getResponseData());
        operationLog.setIpAddr(LogUtil.getIpAddr(httpServletRequest));
        operationLog.setUserAgent(LogUtil.getUserAgent(httpServletRequest));
        operationLog.setStatus(logContext.getStatus());
        operationLog.setMessage(logContext.getMessage());
        log.info("用户操作日志:{}", JSON.toJSONString(operationLog));
    }
}
