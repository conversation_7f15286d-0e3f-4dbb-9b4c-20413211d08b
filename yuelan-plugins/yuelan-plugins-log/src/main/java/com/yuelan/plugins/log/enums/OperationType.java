package com.yuelan.plugins.log.enums;

import com.yuelan.result.able.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 操作类型
 */
@Getter
@AllArgsConstructor
public enum OperationType implements IEnum<OperationType> {

    OTHER(0, "其它"),
    QUERY(1, "查询"),
    INSERT(2, "新增"),
    UPDATE(3, "修改"),
    DELETE(4, "删除"),

    LOGIN(5, "登录"),
    LOGOUT(6, "登出"),
    ;
    private Integer code;
    private String desc;

    @Override
    public OperationType getDefault() {
        return null;
    }
}
