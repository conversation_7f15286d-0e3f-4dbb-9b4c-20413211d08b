package com.yuelan.hermes.quanyi.remote.hbsk.request;

import lombok.Data;

/**
 * 教育包订单确认请求
 *
 * <AUTHOR> 2025/8/11
 * @since 2025/8/11
 */
@Data
public class EduOrderConfirmReq {

    /**
     * 手机号（加密）
     */
    private String phone;

    /**
     * 应用ID
     */
    private String app_id;

    /**
     * 来源ID
     */
    private Integer source_id;

    /**
     * 来源名称
     */
    private String source_name;

    /**
     * 销售产品ID
     */
    private String sale_product_id;

    /**
     * 用户代理
     */
    private String ua;

    /**
     * 设备信息
     */
    private String device_info;

    /**
     * 短信验证码
     */
    private String sms_code;

    /**
     * 时间戳
     */
    private Long time_stamp;

    /**
     * CP渠道代码
     */
    private String cp_channel_code;

    /**
     * 签名
     */
    private String sign;

    /**
     * 应用名称
     */
    private String app_name;

    /**
     * 省份支付类型
     */
    private String province_pay_type;
}
