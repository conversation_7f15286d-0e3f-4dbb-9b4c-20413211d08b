package com.yuelan.hermes.quanyi.config.task;

import com.yuelan.hermes.quanyi.common.enums.SuccessStrategyEnum;
import lombok.Builder;
import lombok.Data;

import java.util.Map;

/**
 * <AUTHOR> 2025/3/24
 * @since 2025/3/24
 */
@Data
@Builder
public class HttpTaskRequest {
    // 必填参数
    private String url;
    private String method;
    private String businessType;
    private String businessId;

    // 可选参数
    private String body;
    private Map<String, String> headers;
    private String contentType;
    private String sourceSystem;

    // 配置参数
    @Builder.Default
    private Integer connectTimeout = 5000;

    @Builder.Default
    private Integer socketTimeout = 10000;

    @Builder.Default
    private Integer maxRetryCount = 3;

    /**
     * 重试间隔时间的基础值，单位秒 会指数级增长
     */
    @Builder.Default
    private Integer retryInterval = 300;

    @Builder.Default
    private SuccessStrategyEnum successStrategy = SuccessStrategyEnum.HTTP_STATUS;
}