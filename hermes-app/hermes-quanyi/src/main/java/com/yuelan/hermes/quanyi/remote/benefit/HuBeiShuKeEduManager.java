package com.yuelan.hermes.quanyi.remote.benefit;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> 2025/8/11
 * @since 2025/8/11
 * <p>
 * 湖北数科-教育包
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class HuBeiShuKeEduManager {

    private static final String USER_AGENT = "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1";

    /**
     * 检查手机号是否为电信用户
     */
    public boolean checkIsTelecom(String mobile) {
        String url = "https://api.play.cn/api/v1/cloud_center/" + mobile + "/isTelecom";

        HttpResponse response = HttpRequest.get(url)
                .header("Accept", "application/json, text/plain, */*")
                .header("Accept-Language", "zh-CN,zh;q=0.9")
                .header("Cache-Control", "no-cache")
                .header("Origin", "https://act.play.cn")
                .header("Referer", "https://act.play.cn/")
                .header("User-Agent", USER_AGENT)
                .execute();

        String body = response.body();
        log.info("检查电信用户返回参数: {}", body);

        // 解析返回结果，判断ext字段是否为true
        JSONObject result = JSON.parseObject(body);
        return result.getIntValue("code") == 200
                && result.getBooleanValue("ext");
    }


}
