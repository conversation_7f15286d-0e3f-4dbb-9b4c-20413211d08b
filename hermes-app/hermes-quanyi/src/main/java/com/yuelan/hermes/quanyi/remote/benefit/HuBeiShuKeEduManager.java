package com.yuelan.hermes.quanyi.remote.benefit;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.yuelan.hermes.quanyi.remote.hbsk.request.EduSmsCodeReq;
import com.yuelan.hermes.quanyi.remote.hbsk.response.EduSmsCodeResp;
import com.yuelan.hermes.quanyi.remote.hbsk.response.TelecomCheckResp;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> 2025/8/11
 * @since 2025/8/11
 * <p>
 * 湖北数科-教育包
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class HuBeiShuKeEduManager {

    private static final String USER_AGENT = "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1";

    /**
     * 检查手机号是否为电信用户
     */
    public boolean checkIsTelecom(String mobile) {
        TelecomCheckResp response = checkIsTelecomWithResponse(mobile);
        return response.isTelecomUser();
    }

    /**
     * 检查手机号是否为电信用户（返回完整响应对象）
     */
    public TelecomCheckResp checkIsTelecomWithResponse(String mobile) {
        String url = "https://api.play.cn/api/v1/cloud_center/" + mobile + "/isTelecom";

        log.info("检查电信用户请求地址: {}", url);

        try (HttpResponse response = HttpRequest.get(url)
                .header("Accept", "application/json, text/plain, */*")
                .header("Origin", "https://act.play.cn")
                .header("Referer", "https://act.play.cn/")
                .header("User-Agent", USER_AGENT)
                .execute()) {

            String body = response.body();
            log.info("检查电信用户返回参数: {}", body);

            return JSON.parseObject(body, TelecomCheckResp.class);
        } catch (Exception e) {
            log.error("检查电信用户异常", e);
            TelecomCheckResp errorResp = new TelecomCheckResp();
            errorResp.setCode(500);
            errorResp.setText("检查电信用户异常: " + e.getMessage());
            errorResp.setExt(false);
            return errorResp;
        }
    }

    /**
     * 发送短信验证码
     *
     * @param req 短信验证码请求
     * @return 短信验证码响应
     */
    public EduSmsCodeResp sendSmsCode(EduSmsCodeReq req) {
        String url = "https://api.play.cn/api/v1/sale/product/order/smscode";

        log.info("教育包发送短信验证码请求地址: {}", url);
        log.info("教育包发送短信验证码请求参数: {}", JSON.toJSONString(req));

        try (HttpResponse response = HttpRequest.post(url)
                .header("Accept", "application/json, text/plain, */*")
                .header("Content-Type", "application/json;charset=UTF-8")
                .header("Origin", "https://act.play.cn")
                .header("Referer", "https://act.play.cn/")
                .header("User-Agent", USER_AGENT)
                .body(JSON.toJSONString(req))
                .execute()) {

            String body = response.body();
            log.info("教育包发送短信验证码返回参数: {}", body);

            return JSON.parseObject(body, EduSmsCodeResp.class);
        } catch (Exception e) {
            log.error("教育包发送短信验证码异常", e);
            return EduSmsCodeResp.fail("发送短信验证码异常: " + e.getMessage());
        }
    }


}
