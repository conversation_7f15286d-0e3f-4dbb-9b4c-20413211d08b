package com.yuelan.hermes.quanyi.remote.hbsk.response;

import lombok.Data;

/**
 * 教育包短信验证码响应
 *
 * <AUTHOR> 2025/8/11
 * @since 2025/8/11
 */
@Data
public class EduSmsCodeResp {

    /**
     * 响应码
     */
    private Integer code;

    /**
     * 响应消息
     */
    private String text;

    /**
     * 扩展字段
     */
    private Boolean ext;

    /**
     * 判断是否成功
     */
    public boolean isSuccess() {
        return code != null && code == 200;
    }

    /**
     * 创建成功响应
     */
    public static EduSmsCodeResp success() {
        EduSmsCodeResp response = new EduSmsCodeResp();
        response.setCode(200);
        response.setText("success");
        response.setExt(true);
        return response;
    }

    /**
     * 创建失败响应
     */
    public static EduSmsCodeResp fail(String message) {
        EduSmsCodeResp response = new EduSmsCodeResp();
        response.setCode(500);
        response.setText(message);
        response.setExt(false);
        return response;
    }
}
