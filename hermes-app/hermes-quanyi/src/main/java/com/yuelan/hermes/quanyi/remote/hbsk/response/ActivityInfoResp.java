package com.yuelan.hermes.quanyi.remote.hbsk.response;

import lombok.Data;

/**
 * 活动信息响应
 *
 * <AUTHOR> 2025/8/11
 * @since 2025/8/11
 */
@Data
public class ActivityInfoResp {

    /**
     * 响应码
     */
    private Integer code;

    /**
     * 响应消息
     */
    private String text;

    /**
     * 活动详细信息
     */
    private ActivityDetail ext;

    /**
     * 判断是否成功
     */
    public boolean isSuccess() {
        return code != null && code == 200;
    }

    /**
     * 获取活动信息
     */
    public ActivityDetail getActivityDetail() {
        return ext;
    }
}
