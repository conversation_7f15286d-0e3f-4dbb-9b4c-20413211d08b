package com.yuelan.hermes.quanyi.common.pojo.properties;

import com.yuelan.hermes.quanyi.common.enums.HuBeiShuKePayPkgEnum;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR> 2025/8/11
 * @since 2025/8/11
 */
@Data
@Component
public class HuBeiShuKeEduProperties {

    public static Map<HuBeiShuKePayPkgEnum, EduConf> pkgConfigMap = new ConcurrentHashMap<>();

    static {
        // https://act.play.cn/hd/p/sales/?catid=1034&channelid=1882670132648480821&cp_channel_code=003
        pkgConfigMap.put(HuBeiShuKePayPkgEnum.EDU_BY_THUMB, new EduConf()
                .setAppId("1882670132648480821")
                .setActivityId("1034")
                .setCpChannelCode("003"));

    }
    public static final String SOURCE = "yyx_xm";
    public static final String PHONE_TW_REPORT_URL = "https://yzfwx.wdtszkj.com/temactivities/wap/ai_order!orderExt.action";
    public static final String RSA_PUBLIC_KEY = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCOPdZ5eAJuUN0Biw00BFOyfVxN+wL7QzGWFmCO9UtKWtGjx3K9HKFgWJxRW+xA+JN3Xq+0mJ3fL+uyu0JV+7SSNiiVoyLFbTMlunniZSR1gVF1AU+1h0Hv4zhS2fepWWZYcYC+nRzRSeVTfJwfgwhT0J+TQK93fjOBlcVFI/gaRQIDAQAB";



    /**
     * 落地页链接 比如<a href="https://act.play.cn/hd/p/sales/?catid=1034&channelid=1882670132648480821&cp_channel_code=003">落地页</a>
     */
    @Data
    @Accessors(chain = true)
    public static class EduConf {
        /**
         * appId和 channelId 好像是一个值 1882670132648480821 在落地页 url 的 ChanelId上
         */
        private String appId;

        /**
         * 活动 id = 链接上的 catid
         */
        private String activityId;

        /**
         * cp_channel_code = 链接上的 cp_channel_code
         */
        private String cpChannelCode;
    }


}
