package com.yuelan.hermes.commons.enums;

import com.yuelan.result.able.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <p> 收支类型</p>
 *
 * <AUTHOR>
 * @date 2022/10/18
 */
@Getter
@AllArgsConstructor
public enum TradeTypeEnum implements IEnum<TradeTypeEnum> {
    IN(1, "收入"),
    OUT(2, "支出"),
    ;
    //

    private Integer code;
    private String desc;


    @Override
    public TradeTypeEnum getDefault() {
        return null;
    }
}
