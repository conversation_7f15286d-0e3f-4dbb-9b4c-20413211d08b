package com.yuelan.hermes.commons.excel;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.alibaba.excel.util.NumberUtils;
import com.yuelan.core.util.LocalEnumUtils;
import com.yuelan.hermes.commons.enums.DeliveryTypeEnum;

import java.util.Objects;

/**
 * <AUTHOR> 2025/5/20
 * @since 2025/5/20
 */
public class DeliveryTypeConverter implements Converter<Integer> {
    @Override
    public Class<?> supportJavaTypeKey() {
        return Integer.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    @Override
    public Integer convertToJavaData(ReadCellData<?> cellData, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        DeliveryTypeEnum deliveryType = LocalEnumUtils.findByName(DeliveryTypeEnum.class, cellData.getStringValue());
        return Objects.isNull(deliveryType) ? null : deliveryType.getCode();
    }

    @Override
    public WriteCellData<?> convertToExcelData(Integer value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        DeliveryTypeEnum deliveryType = LocalEnumUtils.findByCode(DeliveryTypeEnum.class, value);
        if (Objects.isNull(deliveryType)) {
            return NumberUtils.formatToCellDataString(value, contentProperty);
        }
        return new WriteCellData<>(deliveryType.getDesc());
    }
}
