package com.yuelan.hermes.commons.excel;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.alibaba.excel.util.NumberUtils;
import com.yuelan.core.util.LocalEnumUtils;
import com.yuelan.hermes.commons.enums.SupplierEnum;
import lombok.extern.slf4j.Slf4j;

import java.util.Objects;

@Slf4j
public class SupplierEnumConverter implements Converter<Integer> {
    @Override
    public Class<?> supportJavaTypeKey() {
        return Integer.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    @Override
    public Integer convertToJavaData(ReadCellData<?> cellData, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        SupplierEnum supplierEnum = LocalEnumUtils.findByName(SupplierEnum.class, cellData.getStringValue());
        return Objects.isNull(supplierEnum) ? null : supplierEnum.getCode();
    }

    @Override
    public WriteCellData<?> convertToExcelData(Integer value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        SupplierEnum supplierEnum = LocalEnumUtils.findByCode(SupplierEnum.class, value);

        if (Objects.isNull(supplierEnum)) {
            return NumberUtils.formatToCellDataString(value, contentProperty);
        }
        return new WriteCellData<>(supplierEnum.getDesc());
    }

}
