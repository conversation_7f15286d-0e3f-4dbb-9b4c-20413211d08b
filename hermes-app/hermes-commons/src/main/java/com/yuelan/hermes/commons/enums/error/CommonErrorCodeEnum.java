package com.yuelan.hermes.commons.enums.error;

import com.yuelan.result.able.IErrorCode;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum CommonErrorCodeEnum implements IErrorCode<CommonErrorCodeEnum> {

    RATELIMITER("2000", "限流器限流"),
    DB_ERROR("2001", "数据库异常"),
    RESUBMIT_ERROR("2002", "重复提交"),
    DOWNLOAD_FAILURE("2003", "下载文件失败"),
    IMPORT_FILE_FAILURE("2004", "文件导入失败"),
    ;

    private String code;
    private String desc;

}
