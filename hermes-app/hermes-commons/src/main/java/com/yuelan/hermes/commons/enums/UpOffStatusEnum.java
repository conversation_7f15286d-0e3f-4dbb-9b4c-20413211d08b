package com.yuelan.hermes.commons.enums;

import com.yuelan.result.able.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <p> 上下架状态 </p>
 */
@Getter
@AllArgsConstructor
public enum UpOffStatusEnum implements IEnum<UpOffStatusEnum> {
    OFF(0, "下架"),
    UP(1, "上架"),
    ;
    private Integer code;
    private String desc;

    @Override
    public UpOffStatusEnum getDefault() {
        return OFF;
    }
}
