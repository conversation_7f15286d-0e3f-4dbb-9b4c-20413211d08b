package com.yuelan.hermes.commons.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> 2024/4/9 下午7:56
 *
 * 短信验证码类型
 */
@Getter
@AllArgsConstructor
public enum SmsCodeType {

    ECC_USER_BIND(2, "电商卡-用户绑定验证码", 10, 10, 3, 0),
    ADMIN_LOGIN(3, "管理员登录验证码", 10, 10, 3, 0),
    BENEFIT_USER_LOGIN(4, "权益N选1-用户登录验证码", 10, 10, 3, 20),
    BENEFIT_USER_ORDER(5, "权益N选1-下单验证码", 10, 10, 3, 20),
    HSH_USER_VERIFY(6, "惠生活-身份验证", 10, 10, 3, 0),
    GAMING_SMS_CODE(99, "电竞卡-身份验证", 10, 10, 3, 20),

    ;

    private final int type;
    private final String desc;

    /**
     * 验证码过期时间的分钟数
     */
    private final int expireMinutes;

    /**
     * 每日发送次数
     */
    private final int dayLimit;

    /**
     * 验证码最多验证几次
     */
    private final int verifyFailLimit;

    /**
     * ip发送短信次数 0表示不限制 比如不是用户主动输入手机号码的地方可以不限制
     */
    private final int ipSendLimit;

}
