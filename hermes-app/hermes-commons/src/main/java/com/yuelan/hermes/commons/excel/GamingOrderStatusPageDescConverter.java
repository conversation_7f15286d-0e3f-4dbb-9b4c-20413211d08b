package com.yuelan.hermes.commons.excel;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.yuelan.result.entity.KeyValue;

import java.util.Objects;

/**
 * <AUTHOR> 2025/5/20
 * @since 2025/5/20
 * 、、KeyValue<Integer, String> orderStatus
 */
public class GamingOrderStatusPageDescConverter implements Converter<KeyValue<Integer, String>> {

    @Override
    public Class<?> supportJavaTypeKey() {
        return KeyValue.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    @Override
    public KeyValue<Integer, String> convertToJavaData(ReadCellData<?> cellData, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        return null;
    }

    @Override
    public WriteCellData<?> convertToExcelData(KeyValue<Integer, String> keyValue, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        Integer key = keyValue.getKey();
        // '0': { text: '兑换中', tagType: 'warning' },
        //     '1': { text: '兑换完毕', tagType: 'success' },
        //     '2': { text: '不可兑换', tagType: 'danger' },
        //     '3': { text: '兑换异常', tagType: 'danger' }
        String desc = "";
        if (Objects.equals(key, 0)) {
            desc = "兑换中";
        } else if (Objects.equals(key, 1)) {
            desc = "兑换完毕";
        } else if (Objects.equals(key, 2)) {
            desc = "不可兑换";
        } else if (Objects.equals(key, 3)) {
            desc = "兑换异常";
        }
        return new WriteCellData<>(desc);
    }
}


