package com.yuelan.hermes.commons.util;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.crypto.digest.DigestAlgorithm;
import cn.hutool.crypto.digest.Digester;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <p> 签名 </p>
 */
@Slf4j
public class YlSignUtil {

    /**
     * 等于符号标志
     */
    private static final String EQ_SYMBOL = "=";

    /**
     * 并且符号标志
     */
    private static final String AND_SYMBOL = "&";

    /**
     * 获取签名原串
     * <p>1.排除某些字符串Key值</p>
     * <p>2.根据key对传来的map数据排序</p>
     * <p>3.生成a1=b1&a2=b2&a3=b3形式的字符串</p>
     *
     * @param params         要排序的参数
     * @param excludeKeyList 要排除的key值
     * @return 签名原串
     */
    public static String getSignData(Map<String, Object> params, List<String> excludeKeyList) {
        Map<String, Object> map = Maps.newHashMap(params);
        if (CollectionUtil.isNotEmpty(excludeKeyList)) {
            for (String key : excludeKeyList) {
                map.remove(key);
            }
        }
        return MapUtil.join(MapUtil.sort(map), AND_SYMBOL, EQ_SYMBOL, true);
    }

    /**
     * MD5签名
     *
     * @param secretKey      密钥
     * @param params         要排序的参数
     * @param excludeKeyList 要排除的key值
     * @return 签名摘要
     */
    public static String signMD5(String secretKey, Map<String, Object> params, List<String> excludeKeyList) {
        String str1 = getSignData(params, excludeKeyList);
        String str2 = str1 + "&key=" + secretKey;
        String sign = new Digester(DigestAlgorithm.MD5).digestHex(str2);
        if (log.isDebugEnabled()) {
            log.info("签名原始字符串：{}", str1);
            log.info("签名拼接密钥字符串：{}", str2);
            log.info("签名：{}", sign);
        }
        return sign;
    }

    /**
     * 校验签名
     *
     * @param secretKey      密钥
     * @param params         请求参数
     * @param excludeKeyList 要排除的key值
     * @param sign           签名摘要
     * @return 校验结果
     */
    public static boolean check(String secretKey, Map<String, Object> params, List<String> excludeKeyList, String sign) {
        String md5 = signMD5(secretKey, params, excludeKeyList);
        return Objects.equals(sign, md5);
    }
}