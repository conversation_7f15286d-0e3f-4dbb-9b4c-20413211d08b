package com.yuelan.hermes.commons.enums;

import com.yuelan.result.able.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ObtainStatusEnum implements IEnum<ObtainStatusEnum> {
    WAIT_OBTAINED(0, "待领取"),
    SUCCESS_OBTAINED(1, "领取成功"),
    FAIL_OBTAINED(2, "领取失败"),
    // 领取中
    OBTAINING(3, "领取中")

    ;

    private final Integer code;
    private final String desc;

    @Override
    public ObtainStatusEnum getDefault() {
        return null;
    }
}