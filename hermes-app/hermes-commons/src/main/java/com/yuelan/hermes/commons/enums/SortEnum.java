package com.yuelan.hermes.commons.enums;

import com.yuelan.result.able.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;


@Getter
@AllArgsConstructor
public enum SortEnum implements IEnum<SortEnum> {
    ASC(1, "asc", "升序"),
    DESC(2, "desc", "降序");

    private Integer code;
    private String direction;
    private String desc;

    @Override
    public SortEnum getDefault() {
        return DESC;
    }
}
