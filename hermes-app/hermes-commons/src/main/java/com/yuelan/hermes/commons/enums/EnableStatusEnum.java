package com.yuelan.hermes.commons.enums;

import com.yuelan.result.able.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;


@Getter
@AllArgsConstructor
public enum EnableStatusEnum implements IEnum<EnableStatusEnum> {
    DISABLE(0, "禁用"),
    ENABLE(1, "启用");

    private Integer code;
    private String desc;


    @Override
    public EnableStatusEnum getDefault() {
        return ENABLE;
    }
}
