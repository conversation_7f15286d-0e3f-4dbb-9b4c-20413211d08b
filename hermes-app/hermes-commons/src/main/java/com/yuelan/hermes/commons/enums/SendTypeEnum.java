package com.yuelan.hermes.commons.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> 2024/3/22 16:21
 * <p>
 * 0原始订单 1补发
 */
@Getter
@AllArgsConstructor
public enum SendTypeEnum {

    ORIGINAL_ORDER(1, "原始订单发送"),
    REISSUE_SEND(2, "订单补发"),
    ;
    private final Integer type;
    private final String desc;

    public static SendTypeEnum of(Integer type) {
        for (SendTypeEnum value : values()) {
            if (value.getType().equals(type)) {
                return value;
            }
        }
        return null;
    }

}
