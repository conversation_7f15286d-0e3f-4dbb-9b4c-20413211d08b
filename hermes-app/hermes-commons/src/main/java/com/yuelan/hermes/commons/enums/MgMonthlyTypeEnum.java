package com.yuelan.hermes.commons.enums;

import com.yuelan.result.able.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> 2024/9/2 15:59
 * 下发类型：0-首次订购，1-连续包月
 */
@AllArgsConstructor
@Getter
public enum MgMonthlyTypeEnum implements IEnum<MgMonthlyTypeEnum> {

    FIRST_SUBSCRIBE(0, "首次订购/点播"),
    RENEWAL_SUBSCRIBE(1, "续订")
    ;

    private final Integer code;
    private final String desc;

    public static MgMonthlyTypeEnum of(Integer code) {
        for (MgMonthlyTypeEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    @Override
    public MgMonthlyTypeEnum getDefault() {
        return null;
    }


}
