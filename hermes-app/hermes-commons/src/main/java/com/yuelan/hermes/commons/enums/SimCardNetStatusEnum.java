package com.yuelan.hermes.commons.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR> 2024/5/10 上午10:44
 * <p>
 * sim卡在网状态
 * 1,开通
 * 2,已停机
 * 3,已销户
 */
@Getter
@AllArgsConstructor
public enum SimCardNetStatusEnum {


    OPEN(1, "开通"),
    STOP(2, "停机"),
    CANCEL(3, "销户");

    private final Integer code;
    private final String desc;

    public static SimCardNetStatusEnum of(Integer code) {
        for (SimCardNetStatusEnum value : values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }

    public static SimCardNetStatusEnum likeDesc(String desc) {
        if (Objects.isNull(desc)) {
            return null;
        }
        for (SimCardNetStatusEnum value : values()) {
            if (desc.contains(value.desc)) {
                return value;
            }
        }
        return null;
    }


}
