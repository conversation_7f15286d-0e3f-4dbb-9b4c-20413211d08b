package com.yuelan.hermes.commons.enums;

import com.yuelan.result.able.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum OrderModelEnum implements IEnum<OrderModelEnum> {
    NO_TEMPLATE(0, "无充值模板"),
    SMS_TEMPLATE(1, "模板导入（有短信）"),
    TEMPLATE(2, "模板导入（无短信）"),
    ;

    private Integer code;
    private String desc;

    @Override
    public OrderModelEnum getDefault() {
        return null;
    }
}