package com.yuelan.hermes.commons.enums;

import com.yuelan.result.able.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

@Getter
@AllArgsConstructor
public enum PreorderStatusEnum implements IEnum<PreorderStatusEnum> {
    DEFAULT(0, "预下单默认状态"),
    SUCCESS(1, "预下单成功"),
    FAIL(2, "预下单失败"),
    ;

    private final Integer code;
    private final String desc;

    @Override
    public PreorderStatusEnum getDefault() {
        return null;
    }

    public static PreorderStatusEnum of(Integer code) {
        for (PreorderStatusEnum valueEnum : PreorderStatusEnum.values()) {
            if (Objects.equals(valueEnum.getCode(), code)) {
                return valueEnum;
            }
        }
        return null;
    }
}