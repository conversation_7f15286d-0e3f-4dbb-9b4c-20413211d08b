package com.yuelan.hermes.commons.util;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.mail.MailUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.builder.ExcelWriterBuilder;
import com.alibaba.excel.write.builder.ExcelWriterSheetBuilder;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.alibaba.fastjson2.JSON;
import com.yuelan.hermes.commons.constant.EmailConstants;
import com.yuelan.hermes.commons.enums.error.CommonErrorCodeEnum;
import com.yuelan.hermes.commons.excel.plugin.ExcelSelected;
import com.yuelan.hermes.commons.excel.plugin.ExcelSelectedResolve;
import com.yuelan.hermes.commons.excel.plugin.SelectedSheetWriteHandler;
import com.yuelan.result.entity.BizResult;
import com.yuelan.result.enums.error.BaseErrorCodeEnum;
import com.yuelan.result.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.VerticalAlignment;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.OutputStream;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.util.*;

@Slf4j
public class EasyExcelUtil {


    private static final int MAXROWS = 500000;

    /**
     * 导出
     */
    public static void download(HttpServletResponse response, String fileName, Class clazz, List dataList) throws IOException {
        try {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            // 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
            String name = URLEncoder.encode(fileName + "-" + DateUtil.format(new Date(), "yyyyMMdd"), "UTF-8").replaceAll("\\+", "%20");
            //response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + name + ".xlsx");
            response.setHeader("Content-disposition", "attachment;filename=" + name + ExcelTypeEnum.XLSX.getValue());
            response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
            LongestMatchColumnWidthStyleStrategy longestMatchColumnWidthStyleStrategy =
                    new LongestMatchColumnWidthStyleStrategy();
            // 这里需要设置不关闭流
            EasyExcel.write(response.getOutputStream(), clazz).autoCloseStream(Boolean.FALSE).sheet("sheet1")
                    .registerWriteHandler(longestMatchColumnWidthStyleStrategy).doWrite(dataList);
        } catch (Exception e) {
            log.error("导出{}Excel失败", fileName, e);
            // 重置response
            response.reset();
            response.setContentType("application/json");
            response.setCharacterEncoding("utf-8");
            response.getWriter().println(JSON.toJSONString(BizResult.error(CommonErrorCodeEnum.DOWNLOAD_FAILURE)));
        }
    }


    public static String getString(Object value) {
        return Objects.isNull(value) ? "" : value.toString();
    }

    public static Long getLong(Object value) {
        return Objects.isNull(value) ? null : Long.valueOf(value.toString());
    }

    public static Date getDate(Object value) {
        if (Objects.isNull(value)) {
            return null;
        }
        if (value instanceof Date) {
            return (Date) value;
        } else if (value instanceof String) {
            return DateUtil.parse(value.toString());
        }
        throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "非时间格式");
    }

    public static BigDecimal getBigDecimal(Object value) {
        if (Objects.isNull(value)) {
            return null;
        }
        BigDecimal ret = null;
        if (value instanceof String) {
            ret = NumberUtil.toBigDecimal((String) value);
        } else if (value instanceof Number) {
            ret = NumberUtil.toBigDecimal((Number) value);
        } else {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "非数字格式");
        }
        return ret;
    }


    public static void sendEmail(String title, String to, Class head, Collection<?> data) {
        String fileName = title + System.currentTimeMillis() + ".xlsx";
        String tmpDirPath = FileUtil.getTmpDirPath();
        if (!StrUtil.endWith(tmpDirPath, "/")) {
            tmpDirPath = tmpDirPath + "/";
        }
        File file = FileUtil.file(tmpDirPath + fileName);
        EasyExcel.write(file, head).sheet("sheet1").doWrite(data);
        MailUtil.send(EmailConstants.MAIL_ACCOUNT, to, title, fileName, false, file);
    }


    /**
     * 导出数据
     *
     * @param head      类名
     * @param excelname excel名字
     * @param data      数据
     *                  java.lang.IllegalArgumentException: Invalid row number (1048576) outside allowa
     *                  这是由于Excel的一张sheet允许的最大行数是1048575，由于导出的数据比较大，超出了一张sheet所能容纳的最大行数，导致无法继续创建新的行
     *                  1048575
     *                  1000000
     */
    public static void excelExport(HttpServletResponse response, Class head, String excelname, List data) {
        // 这里注意 有同学反应使用swagger 会导致各种问题，请直接用浏览器或者用postman
        //response.setContentType("application/vnd.ms-excel");
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        try {
            // 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
            String fileName = URLEncoder.encode(excelname, "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            EasyExcel.write(response.getOutputStream(), head).sheet("Sheet1").doWrite(data);
        } catch (IOException e) {
            log.error("导出数据失败", e);
        }
    }

    /**
     * 获取默认表头内容的样式
     *
     * @return
     */
    private static HorizontalCellStyleStrategy getDefaultHorizontalCellStyleStrategy() {
        /** 表头样式 **/
        WriteCellStyle headWriteCellStyle = new WriteCellStyle();
        // 背景色（浅灰色）
        // 可以参考：https://www.cnblogs.com/vofill/p/11230387.html
        headWriteCellStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        // 字体大小
        WriteFont headWriteFont = new WriteFont();
        headWriteFont.setFontHeightInPoints((short) 10);
        headWriteCellStyle.setWriteFont(headWriteFont);
        //设置表头居中对齐
        headWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
        /** 内容样式 **/
        WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
        // 内容字体样式（名称、大小）
        WriteFont contentWriteFont = new WriteFont();
        contentWriteFont.setFontName("宋体");
        contentWriteFont.setFontHeightInPoints((short) 10);
        contentWriteCellStyle.setWriteFont(contentWriteFont);
        //设置内容垂直居中对齐
        contentWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        //设置内容水平居中对齐
        contentWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
        //设置边框样式
        contentWriteCellStyle.setBorderLeft(BorderStyle.THIN);
        contentWriteCellStyle.setBorderTop(BorderStyle.THIN);
        contentWriteCellStyle.setBorderRight(BorderStyle.THIN);
        contentWriteCellStyle.setBorderBottom(BorderStyle.THIN);
        // 头样式与内容样式合并
        return new HorizontalCellStyleStrategy(headWriteCellStyle, contentWriteCellStyle);
    }


    /**
     * 将数据分sheet进行导出
     *
     * @param data     查询结果
     * @param fileName 导出文件名称
     * @param clazz    映射实体class类
     * @param <T>      查询结果类型
     * @throws Exception
     */
    public static <T> void excelExportDivisionBySheet(HttpServletResponse response, Class clazz, String fileName, List<T> data) {
        OutputStream out = null;
        ExcelWriter excelWriter = null;
        try {
            // 分割的集合
            List<List<T>> lists = CollectionUtil.split(data, MAXROWS);
            out = getOutputStream(fileName, response);
            ExcelWriterBuilder excelWriterBuilder =
                    EasyExcel.write(out, clazz).excelType(ExcelTypeEnum.XLSX).registerWriteHandler(getDefaultHorizontalCellStyleStrategy());
            excelWriter = excelWriterBuilder.build();
            ExcelWriterSheetBuilder excelWriterSheetBuilder;
            WriteSheet writeSheet;
            for (int i = 1; i <= lists.size(); i++) {
                excelWriterSheetBuilder = new ExcelWriterSheetBuilder();
                excelWriterSheetBuilder.sheetNo(i);
                excelWriterSheetBuilder.sheetName("sheet" + i);
                writeSheet = excelWriterSheetBuilder.build();
                excelWriter.write(lists.get(i - 1), writeSheet);
            }
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (excelWriter != null) {
                excelWriter.finish();
            }
            if (out != null) {
                try {
                    out.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }


    private static OutputStream getOutputStream(String fileName, HttpServletResponse response) throws IOException {
        fileName = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
        //  response.setContentType("application/vnd.ms-excel"); // .xls
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"); // .xlsx
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
        return response.getOutputStream();
    }


    /**
     * 创建即将导出的sheet页（sheet页中含有带下拉框的列）
     *
     * @param head      导出的表头信息和配置
     * @param sheetNo   sheet索引
     * @param sheetName sheet名称
     * @param <T>       泛型
     * @return sheet页
     */
    public static <T> WriteSheet writeSelectedSheet(Class<T> head, Integer sheetNo, String sheetName) {
        Map<Integer, ExcelSelectedResolve> selectedMap = resolveSelectedAnnotation(head);

        return EasyExcel.writerSheet(sheetNo, sheetName)
                .head(head)
                .registerWriteHandler(new SelectedSheetWriteHandler(selectedMap))
                .build();
    }

    /**
     * 解析表头类中的下拉注解
     *
     * @param head 表头类
     * @param <T>  泛型
     * @return Map<下拉框列索引, 下拉框内容> map
     */
    private static <T> Map<Integer, ExcelSelectedResolve> resolveSelectedAnnotation(Class<T> head) {
        Map<Integer, ExcelSelectedResolve> selectedMap = new HashMap<>();

        // getDeclaredFields(): 返回全部声明的属性；getFields(): 返回public类型的属性
        Field[] fields = head.getDeclaredFields();
        for (int i = 0; i < fields.length; i++) {
            Field field = fields[i];
            // 解析注解信息
            ExcelSelected selected = field.getAnnotation(ExcelSelected.class);
            ExcelProperty property = field.getAnnotation(ExcelProperty.class);
            if (selected != null) {
                ExcelSelectedResolve excelSelectedResolve = new ExcelSelectedResolve();
                String[] source = excelSelectedResolve.resolveSelectedSource(selected);
                if (source != null && source.length > 0) {
                    excelSelectedResolve.setSource(source);
                    excelSelectedResolve.setFirstRow(selected.firstRow());
                    excelSelectedResolve.setLastRow(selected.lastRow());
                    if (property != null && property.index() >= 0) {
                        selectedMap.put(property.index(), excelSelectedResolve);
                    } else {
                        selectedMap.put(i, excelSelectedResolve);
                    }
                }
            }
        }

        return selectedMap;
    }

}
