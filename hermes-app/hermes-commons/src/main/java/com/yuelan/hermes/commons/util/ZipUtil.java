package com.yuelan.hermes.commons.util;

import lombok.extern.slf4j.Slf4j;

import java.io.*;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * <AUTHOR>
 * @create 2018/5/28
 */
@Slf4j
public class ZipUtil {
    private static final int BUFFER_SIZE = 2 * 1024;

    /**
     * 将磁盘的多个文件打包成压缩包并输出流下载
     *
     * @param files
     * @param out
     */
    public static void zipDirFileToFile(List<File> files, OutputStream out) throws IOException {
        try (ZipOutputStream zos = new ZipOutputStream(out)) {
            for (File file : files) {
                InputStream inputStream = new FileInputStream(file);
                setByteArrayOutputStream(file.getName(), inputStream, zos);
            }
            zos.finish();
        }
    }

    private static void setByteArrayOutputStream(String fileName, InputStream inputStream, ZipOutputStream zos) throws IOException {
        try (ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
            byte[] buffer = new byte[BUFFER_SIZE];
            int len;
            while ((len = inputStream.read(buffer)) != -1) {
                baos.write(buffer, 0, len);
            }
            baos.flush();
            byte[] bytes = baos.toByteArray();

            //设置文件名
            zos.putNextEntry(new ZipEntry(fileName));
            zos.write(bytes);
            zos.closeEntry();
        }
    }

}
