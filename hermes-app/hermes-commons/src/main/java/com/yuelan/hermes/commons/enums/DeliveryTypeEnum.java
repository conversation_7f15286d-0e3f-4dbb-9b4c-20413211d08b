package com.yuelan.hermes.commons.enums;

import com.yuelan.result.able.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> 2024/5/3 上午12:07
 * <p>
 * 发货方式：1-直充，2-兑换码
 */
@Getter
@AllArgsConstructor
public enum DeliveryTypeEnum implements IEnum<EnableStatusEnum> {

    DIRECT_RECHARGE(1, "直充"),
    REDEEM_CODE(2, "兑换码"),
    ;

    private final Integer code;
    private final String desc;


    public static DeliveryTypeEnum of(Integer type) {
        for (DeliveryTypeEnum value : values()) {
            if (value.code.equals(type)) {
                return value;
            }
        }
        return null;
    }

    @Override
    public EnableStatusEnum getDefault() {
        return null;
    }
}
