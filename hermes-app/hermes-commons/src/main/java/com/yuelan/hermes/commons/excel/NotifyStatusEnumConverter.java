package com.yuelan.hermes.commons.excel;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.alibaba.excel.util.NumberUtils;
import com.yuelan.core.util.LocalEnumUtils;
import com.yuelan.hermes.commons.enums.NotifyStatusEnum;

import java.util.Objects;

public class NotifyStatusEnumConverter implements Converter<Integer> {

    @Override
    public Class<?> supportJavaTypeKey() {
        return Integer.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    @Override
    public Integer convertToJavaData(ReadCellData<?> cellData, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        NotifyStatusEnum notifyStatusEnum = LocalEnumUtils.findByName(NotifyStatusEnum.class, cellData.getStringValue());
        return Objects.isNull(notifyStatusEnum) ? null : notifyStatusEnum.getCode();
    }

    @Override
    public WriteCellData<?> convertToExcelData(Integer value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        NotifyStatusEnum notifyStatusEnum = LocalEnumUtils.findByCode(NotifyStatusEnum.class, value);
        if (Objects.isNull(notifyStatusEnum)) {
            return NumberUtils.formatToCellDataString(value, contentProperty);
        }
        return new WriteCellData<>(notifyStatusEnum.getDesc());
    }
}
