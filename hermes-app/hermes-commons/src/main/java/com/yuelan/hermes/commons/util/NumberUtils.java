package com.yuelan.hermes.commons.util;

import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

public class NumberUtils {


    public static Long toLong(Integer number) {
        if (Objects.isNull(number)) {
            return null;
        }
        return Long.valueOf(number);
    }

    public static Long valueOf(String number) {
        if (StringUtils.isBlank(number)) {
            return null;
        }
        return Long.valueOf(number);
    }

    public static Integer toInt(Long number) {
        return toInt(number, null);
    }

    public static Integer toInt(Long number, Integer defaultValue) {
        if (number == null) {
            return defaultValue;
        }
        return number.intValue();
    }
}
