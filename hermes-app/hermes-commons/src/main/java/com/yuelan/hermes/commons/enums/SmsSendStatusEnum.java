package com.yuelan.hermes.commons.enums;

import com.yuelan.result.able.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum SmsSendStatusEnum implements IEnum<SmsSendStatusEnum> {
    WAIT(0, "待发送"),
    SUCCESS(1, "已发送"),
    FAIL(2, "发送失败"),
    ;

    private Integer code;
    private String desc;

    @Override
    public SmsSendStatusEnum getDefault() {
        return null;
    }

    public static SmsSendStatusEnum of(Integer code) {
        for (SmsSendStatusEnum statusEnum : values()) {
            if (statusEnum.getCode().equals(code)) {
                return statusEnum;
            }
        }
        return null;
    }
}