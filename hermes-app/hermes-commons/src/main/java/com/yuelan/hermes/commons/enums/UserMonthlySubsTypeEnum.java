package com.yuelan.hermes.commons.enums;

import lombok.Getter;

/**
 * <AUTHOR> 2024/3/29 17:22
 * 用户包月订阅类型
 * 0-首次订购，1-连续包月
 */
@Getter
public enum UserMonthlySubsTypeEnum {


    FIRST(0, "首订"),
    RENEW(1, "续订"),
    ;

    private final Integer type;
    private final String desc;

    UserMonthlySubsTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static UserMonthlySubsTypeEnum of(Integer type) {
        for (UserMonthlySubsTypeEnum value : values()) {
            if (value.type.equals(type)) {
                return value;
            }
        }
        return null;
    }


}
