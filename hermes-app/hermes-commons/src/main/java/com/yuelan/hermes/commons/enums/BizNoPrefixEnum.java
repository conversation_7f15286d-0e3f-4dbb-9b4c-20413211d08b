package com.yuelan.hermes.commons.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum BizNoPrefixEnum {
    ORDER_VIRTUAL("OV", "虚拟订单号"),
    MCH_THIRD("M", "商户号"),
    SKU_NO("GS", "商品SKU编号"),
    ORDER_OFFLINE("OF", "线下订单号"),
    ORDER_MOBILE("OM", "话费订单"),
    ORDER_MOBILE_ITEM("ME", "话费订单明细"),
    GAMING_ORDER("OG", "电竞卡订单"),
    GAMING_ORDER_ITEM("GE", "电竞卡订单明细"),
    ZOP_ORDER("ZO", "zop订单号"),
    GD_ORDER("GD", "广电订单"),
    ECC_ORDER("EO", "电商卡订单"),
    ECC_ORDER_ITEM("EE", "电商卡订单明细"),
    ORDER_BENEFIT("OB", "权益订单"),
    ORDER_BENEFIT_ITEM("BE", "权益订单明细"),
    TIK_TOK_ORDER("TO", "抖音订单"),
    ECC_ORDER_NUMBER_CARD("NC", "号卡订单"),
    BENEFIT_PACKAGE_ORDER("BP", "权益包订单"),
    BENEFIT_ITEM_ORDER("BI", "权益订单"),
    THIRD_CHANNEL_ORDER("TC","第三方渠道订单"),
    // 临时订单号
    TEMP_BENEFIT_ORDER("TBO", "临时订单号"),
    ;

    private String prefix;
    private String desc;


}
