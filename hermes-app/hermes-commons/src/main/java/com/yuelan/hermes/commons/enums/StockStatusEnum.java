package com.yuelan.hermes.commons.enums;

import com.yuelan.result.able.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;


@Getter
@AllArgsConstructor
public enum StockStatusEnum implements IEnum<StockStatusEnum> {
    IN_STOCK(1, "入库"),
    OUT_STOCK(2, "出库");

    private Integer code;
    private String desc;

    @Override
    public StockStatusEnum getDefault() {
        return null;
    }
}
