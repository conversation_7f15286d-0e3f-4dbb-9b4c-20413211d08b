package com.yuelan.hermes.commons.enums;

import com.yuelan.result.able.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum AlgorithmEnum implements IEnum<AlgorithmEnum> {
    MD5(0, "MD5"),
    RSA(1, "RSA"),
    SM4(4, "SM4"),
    ;

    private Integer code;
    private String desc;


    @Override
    public AlgorithmEnum getDefault() {
        return MD5;
    }
}
