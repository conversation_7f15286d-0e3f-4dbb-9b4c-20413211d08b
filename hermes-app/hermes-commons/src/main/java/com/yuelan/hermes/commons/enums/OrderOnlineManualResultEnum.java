package com.yuelan.hermes.commons.enums;

import com.yuelan.result.able.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <p> 线上订单人工操作结果 </p>
 *
 * <AUTHOR>
 * @date 2023/5/31
 */
@Getter
@AllArgsConstructor
public enum OrderOnlineManualResultEnum implements IEnum<OrderOnlineManualResultEnum> {
    FAIL(0, "失败"),
    SUCCESS(1, "成功"),
    RETRY(2, "补发"),
    ;

    private Integer code;
    private String desc;

    @Override
    public OrderOnlineManualResultEnum getDefault() {
        return null;
    }
}
