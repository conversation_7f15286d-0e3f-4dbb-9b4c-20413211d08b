package com.yuelan.hermes.commons.enums;

import lombok.Getter;

/**
 * <AUTHOR> 2024/5/3 上午12:07
 * <p>
 * 兑换方式：1-直充无库存，2-兑换码
 */
@Getter
public enum RedeemTypeEnum {

    DIRECT_RECHARGE(1, "直充无库存"),
    REDEEM_CODE(2, "兑换码"),
    ;

    private final Integer code;
    private final String desc;

    RedeemTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static RedeemTypeEnum of(Integer code) {
        for (RedeemTypeEnum value : values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }


}
