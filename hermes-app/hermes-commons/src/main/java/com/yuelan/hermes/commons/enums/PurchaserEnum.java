package com.yuelan.hermes.commons.enums;

import com.yuelan.result.able.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <p> 采购商 </p>
 *
 * <AUTHOR>
 * @date 2023/3/7
 */

@Getter
@AllArgsConstructor
public enum PurchaserEnum implements IEnum<PurchaserEnum> {
    RONG_SHU(1, "荣数"),
    YUE_LAN(2, "悦蓝"),
    HN_XINJIN(3, "河南信金"),
    ;

    private Integer code;
    private String desc;

    @Override
    public PurchaserEnum getDefault() {
        return null;
    }
}
