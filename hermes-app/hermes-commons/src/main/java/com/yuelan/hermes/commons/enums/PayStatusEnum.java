package com.yuelan.hermes.commons.enums;

import lombok.Getter;

/**
 * <AUTHOR> 2024/4/8 15:07
 * 支付状态，0初始状态 1支付成功 2支付失败
 */
@Getter
public enum PayStatusEnum {

    DEFAULT(0, "等待用户支付"),
    SUCCESS(1, "支付成功"),
    FAIL(2, "支付失败"),
    RENEW(3, "续订"),
    UNSUBSCRIBE(4, "退订"),
    ;

    private final Integer code;
    private final String desc;

    PayStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static PayStatusEnum of(Integer code) {
        for (PayStatusEnum statusEnum : PayStatusEnum.values()) {
            if (statusEnum.getCode().equals(code)) {
                return statusEnum;
            }
        }
        return null;
    }


}
