package com.yuelan.hermes.commons.enums;

import com.yuelan.result.able.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum OrderUseStatusEnum implements IEnum<OrderUseStatusEnum> {
    UNUSED(10, "未使用"),
    USED(11, "已使用"),
    EXPIRED(12, "已过期"),
    VOID(13, "已作废"),
    ;

    private Integer code;
    private String desc;

    @Override
    public OrderUseStatusEnum getDefault() {
        return null;
    }

}