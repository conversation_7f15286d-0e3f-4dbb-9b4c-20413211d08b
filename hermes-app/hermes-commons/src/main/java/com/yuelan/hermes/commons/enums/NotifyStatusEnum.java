package com.yuelan.hermes.commons.enums;

import com.yuelan.result.able.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum NotifyStatusEnum implements IEnum<NotifyStatusEnum> {
    WAIT(0, "等待回调"),
    SUCCESS(1, "回调成功"),
    FAIL(2, "回调失败"),
    ;

    private Integer code;
    private String desc;

    @Override
    public NotifyStatusEnum getDefault() {
        return null;
    }
}