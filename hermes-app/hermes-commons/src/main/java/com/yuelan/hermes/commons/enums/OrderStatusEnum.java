package com.yuelan.hermes.commons.enums;

import com.yuelan.result.able.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

@Getter
@AllArgsConstructor
public enum OrderStatusEnum implements IEnum<OrderStatusEnum> {
    PROCESSING(0, "处理中"),
    SUCCESS(1, "交易成功"),
    FAIL(2, "交易失败"),
    ABNORMAL(3, "订单异常"),//等待人工处理
//    AFTER_SALE(4, "售后"),
    ;

    private Integer code;
    private String desc;

    @Override
    public OrderStatusEnum getDefault() {
        return null;
    }

    public static OrderStatusEnum of(Integer code) {
        for (OrderStatusEnum value : values()) {
            if (Objects.equals(value.getCode(), code)) {
                return value;
            }
        }
        return null;
    }
}