package com.yuelan.hermes.commons.enums;

import com.yuelan.result.able.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <p> 道具兑换订单请求结果 </p>
 *
 * <AUTHOR>
 * @date 2024/1/11
 */
@Getter
@AllArgsConstructor
public enum GamingOrderManualResultEnum implements IEnum<GamingOrderManualResultEnum> {
    FAIL(0, "失败"),
    SUCCESS(1, "成功"),
    RETRY(2, "补发"),
    ;

    private Integer code;
    private String desc;

    @Override
    public GamingOrderManualResultEnum getDefault() {
        return null;
    }
}
