package com.yuelan.hermes.commons.excel;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.yuelan.result.entity.KeyValue;

/**
 * <AUTHOR> 2025/5/20
 * @since 2025/5/20
 */
public class KeyValueConverter implements Converter<KeyValue<Integer, String>> {

    @Override
    public Class<?> supportJavaTypeKey() {
        return KeyValue.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    @Override
    public KeyValue<Integer, String> convertToJavaData(ReadCellData<?> cellData, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        return null;
    }

    @Override
    public WriteCellData<?> convertToExcelData(KeyValue<Integer, String> keyValue, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        return new WriteCellData<>(keyValue.getValue());
    }
}
