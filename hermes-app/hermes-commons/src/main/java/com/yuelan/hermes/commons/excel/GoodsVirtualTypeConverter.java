package com.yuelan.hermes.commons.excel;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.alibaba.excel.util.NumberUtils;
import com.yuelan.core.util.LocalEnumUtils;
import com.yuelan.hermes.commons.enums.GoodsVirtualTypeEnum;

import java.util.Objects;

public class GoodsVirtualTypeConverter implements Converter<Integer> {
    @Override
    public Class<?> supportJavaTypeKey() {
        return Integer.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    @Override
    public Integer convertToJavaData(ReadCellData<?> cellData, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        GoodsVirtualTypeEnum goodsType = LocalEnumUtils.findByName(GoodsVirtualTypeEnum.class, cellData.getStringValue());
        return Objects.isNull(goodsType) ? null : goodsType.getCode();
    }

    @Override
    public WriteCellData<?> convertToExcelData(Integer value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        GoodsVirtualTypeEnum goodsType = LocalEnumUtils.findByCode(GoodsVirtualTypeEnum.class, value);
        if (Objects.isNull(goodsType)) {
            return NumberUtils.formatToCellDataString(value, contentProperty);
        }
        return new WriteCellData<>(goodsType.getDesc());
    }

}
