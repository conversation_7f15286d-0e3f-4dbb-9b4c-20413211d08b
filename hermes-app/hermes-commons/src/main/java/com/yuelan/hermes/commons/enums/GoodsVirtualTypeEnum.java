package com.yuelan.hermes.commons.enums;

import com.yuelan.result.able.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;


@Getter
@AllArgsConstructor
public enum GoodsVirtualTypeEnum implements IEnum<GoodsVirtualTypeEnum> {
    VOUCHER_PASSWORD(1, "卡密"),
    RECHARGE(2, "直沖"),
    PROXY(3, "代销"),
    PREPAID(4, "话费"),
    ;

    private Integer code;
    private String desc;


    @Override
    public GoodsVirtualTypeEnum getDefault() {
        return null;
    }
}
