package com.yuelan.hermes.commons.enums;

import cn.hutool.core.util.StrUtil;
import com.yuelan.result.able.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;


@Getter
@AllArgsConstructor
public enum SpEnum implements IEnum<SpEnum> {
    UNKNWON(0, "未知"),
    CMCC(1, "移动"),
    UNICOM(2, "联通"),
    TELECOM(3, "电信"),
    CBN(4, "广电");

    private Integer code;
    private String desc;

    public static SpEnum findByName(String name) {
        if (StrUtil.isBlank(name)) {
            return UNKNWON;
        }
        if (Objects.equals(CMCC.desc, name)) {
            return CMCC;
        } else if (Objects.equals(UNICOM.desc, name)) {
            return UNICOM;
        } else if (Objects.equals(TELECOM.desc, name)) {
            return TELECOM;
        } else if (Objects.equals(CBN.desc, name)) {
            return CBN;
        }
        return UNKNWON;
    }

    public static SpEnum containsByName(String name) {
        if (StrUtil.isBlank(name)) {
            return UNKNWON;
        }
        if (name.contains(CMCC.desc)) {
            return CMCC;
        } else if (name.contains(UNICOM.desc)) {
            return UNICOM;
        } else if (name.contains(TELECOM.desc)) {
            return TELECOM;
        } else if (name.contains(CBN.desc)) {
            return CBN;
        }
        return UNKNWON;
    }

    @Override
    public SpEnum getDefault() {
        return UNKNWON;
    }
}
