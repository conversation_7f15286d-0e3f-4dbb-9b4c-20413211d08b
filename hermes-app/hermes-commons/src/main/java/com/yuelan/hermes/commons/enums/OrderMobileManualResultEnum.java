package com.yuelan.hermes.commons.enums;

import com.yuelan.result.able.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <p> 话费订单人工操作结果 </p>
 *
 * <AUTHOR>
 * @date 2023/5/31
 */
@Getter
@AllArgsConstructor
public enum OrderMobileManualResultEnum implements IEnum<OrderMobileManualResultEnum> {
    FAIL(0, "充值失败"),
    SUCCESS(1, "充值成功"),
    RETRY(2, "重试"),
    ;

    private Integer code;
    private String desc;

    @Override
    public OrderMobileManualResultEnum getDefault() {
        return null;
    }
}
