package com.yuelan.hermes.commons.excel;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.alibaba.excel.util.NumberUtils;
import com.yuelan.core.util.LocalEnumUtils;
import com.yuelan.hermes.commons.enums.RechargeTypeEnum;

import java.util.Objects;

public class RechargeTypeEnumConverter implements Converter<Integer> {

    @Override
    public Class<?> supportJavaTypeKey() {
        return Integer.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    @Override
    public Integer convertToJavaData(ReadCellData<?> cellData, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        RechargeTypeEnum type = LocalEnumUtils.findByName(RechargeTypeEnum.class, cellData.getStringValue());
        return Objects.isNull(type) ? null : type.getCode();
    }

    @Override
    public WriteCellData<?> convertToExcelData(Integer value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        RechargeTypeEnum type = LocalEnumUtils.findByCode(RechargeTypeEnum.class, value);
        if (Objects.isNull(type)) {
            return NumberUtils.formatToCellDataString(value, contentProperty);
        }
        return new WriteCellData<>(type.getDesc());
    }
}
