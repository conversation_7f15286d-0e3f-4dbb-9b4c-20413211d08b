package com.yuelan.hermes.commons.enums;

import com.yuelan.result.able.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * zop请求通用状态枚举
 */
@Getter
@AllArgsConstructor
public enum ZopOrderStatusEnum implements IEnum<ZopOrderStatusEnum> {
    DEFAULT(0, "默认状态"),
    SUCCESS(1, "请求成功"),
    FAIL(2, "请求失败"),
    ;

    private final Integer code;
    private final String desc;

    public static ZopOrderStatusEnum of(Integer code) {
        for (ZopOrderStatusEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    @Override
    public ZopOrderStatusEnum getDefault() {
        return null;
    }
}