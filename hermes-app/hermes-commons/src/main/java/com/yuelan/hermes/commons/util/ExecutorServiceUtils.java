package com.yuelan.hermes.commons.util;

import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

@Slf4j
public class ExecutorServiceUtils {
    private static final ExecutorService executorService = new ThreadPoolExecutor(
            // 核心线程数
            10,
            // 最大线程数
            20,
            // 空闲线程存活时间
            60,
            TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(1024)
    );

    public static void execute(Runnable task) {
        executorService.execute(task);
    }

    public static long getTotalTasks() {
        // 获取还在等待的任务
        return ((ThreadPoolExecutor) executorService).getTaskCount();
    }

    public static long getWaitAndExecTaskCount() {
        return (long) (((ThreadPoolExecutor) executorService).getActiveCount() + ((ThreadPoolExecutor) executorService).getQueue().size());
    }

    public static void shutdown() {
        executorService.shutdown();
    }

    public static List<Runnable> shutdownNow() {
        return executorService.shutdownNow();
    }

    public static boolean isShutdown() {
        return executorService.isShutdown();
    }

    public static boolean isTerminated() {
        return executorService.isTerminated();
    }
}
