package com.yuelan.hermes.commons.enums;

import com.yuelan.result.able.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;


@Getter
@AllArgsConstructor
public enum AccountStatementBizTypeEnum implements IEnum<AccountStatementBizTypeEnum> {
    ACCOUNT_RECHARGE(1, "账户充值"),
    PREPAID_RECHARGE(2, "话费订单"),
    ;

    private Integer code;
    private String desc;


    @Override
    public AccountStatementBizTypeEnum getDefault() {
        return null;
    }
}
