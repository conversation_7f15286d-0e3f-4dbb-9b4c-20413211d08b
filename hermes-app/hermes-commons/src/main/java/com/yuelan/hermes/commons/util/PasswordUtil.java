package com.yuelan.hermes.commons.util;

import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.RSA;
import com.yuelan.result.enums.error.BaseErrorCodeEnum;
import com.yuelan.result.exception.BizException;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

public class PasswordUtil {

    /**
     * 生成一次性随机码
     */
    public static String generateKey() {
        return IdUtil.fastSimpleUUID();
    }

    /**
     * 解密用户密码
     *
     * @param rsaPassword      用户密码 RSA(MD5(password+salt)+key) key 一次性随机码(uuid)长度32
     * @param privateKeyBase64 私钥
     */
    public static String decrypt(String rsaPassword, String privateKeyBase64) {
        RSA rsa = SecureUtil.rsa(privateKeyBase64, null);
        return rsa.decryptStr(rsaPassword, KeyType.PrivateKey);
    }

    /**
     * 获取随机码
     *
     * @param decryptPassword rsa解密后的加密串
     */
    public static String getKey(String decryptPassword) {
        return StrUtil.subSufByLength(decryptPassword, 32);
    }

    /**
     * 获取加密后的密码
     *
     * @param decryptPassword rsa解密后的加密串
     * @param key             一次性随机码
     */
    public static String getPassword(String decryptPassword, String key) {
        return StrUtil.removeSuffix(decryptPassword, key);
    }

    /**
     * 校验密码是否一致
     *
     * @param decryptPassword rsa解密后的加密串
     * @param md5Password     要比对的密码
     * @param key             一次性随机码
     */
    public static boolean verify(String decryptPassword, String md5Password, String key) {
        String password = getPassword(decryptPassword, key);
        return Objects.equals(md5Password, password);
    }

    /**
     * 明文密码MD5
     *
     * @param password 明文密码
     * @param salt     盐值
     */
    public static String md5Password(String password, String salt) {
        if (StringUtils.isBlank(password)) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "密码不能为空");
        }
        String data = password + salt;
        return SecureUtil.md5(data);
    }

    /**
     * 生成随机密码
     *
     * @param salt 盐值
     * @return <明文密码，MD5后的密码>
     */
    public static Pair<String, String> genPassword(String salt) {
        String password = RandomPwd.getRandomPwd(8);
        String md5 = md5Password(password, salt);
        return Pair.of(password, md5);
    }

}
