package com.yuelan.hermes.commons.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 微信公众号授权类型
 */
@Getter
@AllArgsConstructor
public enum WxMpScopeEnum {

    /**
     * 不弹出授权页面，直接跳转，只能获取用户openid
     */
    SNSAPI_BASE("snsapi_base", "不弹出授权页面，直接跳转，只能获取用户openid"),
    /**
     * 弹出授权页面，可通过openid拿到昵称、性别、所在地
     */
    SNSAPI_USERINFO("snsapi_userinfo", "弹出授权页面，可通过openid拿到昵称、性别、所在地"),
    /**
     * 弹出授权页面，可通过openid拿到昵称、性别、所在地、头像
     */
    SNSAPI_LOGIN("snsapi_login", "弹出授权页面，可通过openid拿到昵称、性别、所在地、头像");

    private final String code;
    private final String desc;

    public static WxMpScopeEnum of(String code) {
        for (WxMpScopeEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }


}
