package com.yuelan.hermes.commons.util;

import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Lists;
import com.yuelan.hermes.commons.enums.SpEnum;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

public class IspUtil {

    public static final List<String> CMCC_PREFIX_LIST = Lists.newArrayList(
            "134", "135", "136", "137", "138", "139", "144", "147", "148",
            "150", "151", "152", "157", "158", "159", "165", "1703", "1705", "1706", "172",
            "178", "182", "183", "184", "187", "188", "195", "197", "198");

    public static final List<String> UNICOM_PREFIX_LIST = Lists.newArrayList(
            "130", "131", "132", "140", "145", "146", "155", "156", "166",
            "167", "1704", "1707", "1708", "1709", "171", "175", "176", "185", "186", "196");

    public static final List<String> TELECOM_PREFIX_LIST = Lists.newArrayList(
            "133", "141", "149", "153", "162", "1700", "1701", "1702",
            "173", "177", "180", "181", "189", "190", "191", "193", "199");

    public static final List<String> CBN_PREFIX_LIST = Lists.newArrayList("192");


    public static SpEnum getIsp(String phone) {
        if (StrUtil.isBlank(phone)) {
            return SpEnum.UNKNWON;
        }
        String prefix = StringUtils.truncate(phone, 3);
        if (CMCC_PREFIX_LIST.contains(prefix)) {
            return SpEnum.CMCC;
        } else if (UNICOM_PREFIX_LIST.contains(prefix)) {
            return SpEnum.UNICOM;
        } else if (TELECOM_PREFIX_LIST.contains(prefix)) {
            return SpEnum.TELECOM;
        } else if (CBN_PREFIX_LIST.contains(prefix)) {
            return SpEnum.CBN;
        }

        return SpEnum.UNKNWON;
    }
}
